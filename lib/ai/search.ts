import { generateText } from 'ai'
import { getModel, DEFAULT_MODEL } from './openrouter'
import { SEARCH_NOTES_PROMPT } from './prompts'
import { Note } from '@/types/notes'
import { replacePlaceholders, prepareNotesForSearch, AI_CONSTANTS } from './utils'
import { aiLogger } from './logger'

export interface SearchResult {
  noteIds: string[]
}

export async function searchNotesWithAI(query: string, notes: Note[]): Promise<string[]> {
  const logContext = {
    operation: 'search' as const,
    model: DEFAULT_MODEL,
    temperature: AI_CONSTANTS.SEARCH_TEMPERATURE,
    'Search query': query,
    'Notes count': notes.length
  }

  try {
    const notesData = prepareNotesForSearch(notes)
    const notesJson = JSON.stringify(notesData, null, 2)

    const prompt = replacePlaceholders(SEARCH_NOTES_PROMPT, {
      query: query,
      notes: notesJson
    })

    aiLogger.logRequest(logContext, prompt)

    const { text } = await generateText({
      model: getModel(DEFAULT_MODEL),
      prompt,
      temperature: AI_CONSTANTS.SEARCH_TEMPERATURE,
    })

    aiLogger.logResponse(logContext, text)

    let result
    try {
      result = JSON.parse(text)
    } catch (parseError) {
      aiLogger.logError(logContext, parseError, { 'Raw AI response': text })
      return []
    }

    if (!result || typeof result !== 'object' || !result.noteIds || !Array.isArray(result.noteIds)) {
      aiLogger.logError(logContext, 'Invalid AI search response format', { result })
      return []
    }

    const filteredIds = result.noteIds.filter((id: string) =>
      notes.some(note => note.id === id)
    )

    aiLogger.logResult(logContext, {
      'Parsed noteIds': result.noteIds,
      'Filtered noteIds': filteredIds
    })

    return filteredIds
  } catch (error) {
    aiLogger.logError(logContext, error)
    return []
  }
}
