import { createOpenRouter } from '@openrouter/ai-sdk-provider';

if (!process.env.OPENROUTER_API_KEY) {
  throw new Error('OPENROUTER_API_KEY environment variable is required but not set')
}

const openrouter = createOpenRouter({
  apiKey: process.env.OPENROUTER_API_KEY,
});

export const DEFAULT_MODEL = 'openai/gpt-4.1-mini'
export const MULTIMODAL_MODEL = 'openai/gpt-4.1-mini'

export function getModel(modelName: string = DEFAULT_MODEL) {
  return openrouter(modelName)
}
