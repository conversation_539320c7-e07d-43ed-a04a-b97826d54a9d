import { generateText } from 'ai'
import { getModel, DEFAULT_MODEL } from './openrouter'
import { ANALYZE_NOTE_PROMPT } from './prompts'
import { generateSimpleTags } from '@/lib/utils/tags'
import { replacePlaceholders, truncateContent, AI_CONSTANTS } from './utils'
import { aiLogger } from './logger'

export interface AnalyzeResult {
  tags: string[]
  summary: string
}

export async function analyzeNote(content: string): Promise<AnalyzeResult> {
  const logContext = {
    operation: 'analyze' as const,
    model: DEFAULT_MODEL,
    temperature: AI_CONSTANTS.ANALYZE_TEMPERATURE
  }

  try {
    const prompt = replacePlaceholders(ANALYZE_NOTE_PROMPT, {
      noteContent: content
    })

    aiLogger.logRequest(logContext, prompt)

    const { text } = await generateText({
      model: getModel(DEFAULT_MODEL),
      prompt,
      temperature: AI_CONSTANTS.ANALYZE_TEMPERATURE,
    })

    aiLogger.logResponse(logContext, text)

    const result = JSON.parse(text)

    if (!result.tags || !Array.isArray(result.tags) || !result.summary) {
      aiLogger.logError(logContext, 'Invalid AI response format', { result })
      throw new Error('Invalid AI response format')
    }

    const finalResult = {
      tags: result.tags.slice(0, AI_CONSTANTS.MAX_TAGS_COUNT),
      summary: result.summary
    }

    aiLogger.logResult(logContext, finalResult)

    return finalResult
  } catch (error) {
    aiLogger.logError(logContext, error)

    const fallbackResult = {
      tags: generateSimpleTags(content),
      summary: truncateContent(content, AI_CONSTANTS.SUMMARY_FALLBACK_LENGTH)
    }

    aiLogger.logFallback(logContext, fallbackResult, 'AI analysis failed')

    return fallbackResult
  }
}
