import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { Note } from '@/types/notes'

function isValidContentType(value: any): value is 'text' | 'file' | 'link' {
  return typeof value === 'string' && ['text', 'file', 'link'].includes(value)
}

export async function GET(
  _request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()

    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const noteId = params.id

    if (!noteId) {
      return NextResponse.json({ error: 'Note ID is required' }, { status: 400 })
    }

    const { data: noteData, error } = await supabase
      .from('notes')
      .select(`
        id,
        content,
        content_type,
        summary_ai,
        created_at,
        updated_at,
        note_tags (
          tags (
            id,
            name
          )
        )
      `)
      .eq('id', noteId)
      .eq('user_id', user.id)
      .single()

    if (error || !noteData) {
      return NextResponse.json({ error: 'Note not found' }, { status: 404 })
    }

    if (!isValidContentType(noteData.content_type)) {
      return NextResponse.json({ error: 'Invalid content type' }, { status: 500 })
    }

    const note: Note = {
      id: noteData.id,
      content: noteData.content,
      content_type: noteData.content_type,
      summary_ai: noteData.summary_ai,
      created_at: noteData.created_at,
      updated_at: noteData.updated_at,
      tags: noteData.note_tags?.map((nt: any) => nt.tags.name) || []
    }

    return NextResponse.json({ note })
  } catch (error) {
    console.error('Error in GET /api/notes/[id]/refresh:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
