import { createClient } from '@/lib/supabase/server'
import { NextRequest, NextResponse } from 'next/server'
import { UpdateNoteRequest, UpdateNoteResponse } from '@/types/notes'

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()

    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const noteId = params.id

    if (!noteId) {
      return NextResponse.json({ error: 'Note ID is required' }, { status: 400 })
    }

    // Проверяем, что заметка принадлежит пользователю
    const { data: note, error: noteError } = await supabase
      .from('notes')
      .select('id, user_id')
      .eq('id', noteId)
      .eq('user_id', user.id)
      .single()

    if (noteError || !note) {
      return NextResponse.json({ error: 'Note not found' }, { status: 404 })
    }

    // Удаляем заметку (связанные теги удалятся автоматически благодаря CASCADE)
    const { error: deleteError } = await supabase
      .from('notes')
      .delete()
      .eq('id', noteId)
      .eq('user_id', user.id)

    if (deleteError) {
      console.error('Error deleting note:', deleteError)
      return NextResponse.json({ error: 'Failed to delete note' }, { status: 500 })
    }

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error in DELETE /api/notes/[id]:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const supabase = await createClient()

    const { data: { user }, error: userError } = await supabase.auth.getUser()
    if (userError || !user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const noteId = params.id

    if (!noteId) {
      return NextResponse.json({ error: 'Note ID is required' }, { status: 400 })
    }

    const { content }: UpdateNoteRequest = await request.json()

    if (!content || !content.trim()) {
      return NextResponse.json({ error: 'Content is required' }, { status: 400 })
    }

    // Проверяем, что заметка принадлежит пользователю
    const { data: existingNote, error: noteError } = await supabase
      .from('notes')
      .select('id, user_id')
      .eq('id', noteId)
      .eq('user_id', user.id)
      .single()

    if (noteError || !existingNote) {
      return NextResponse.json({ error: 'Note not found' }, { status: 404 })
    }

    // Обновляем заметку
    const { data: updatedNote, error: updateError } = await supabase
      .from('notes')
      .update({
        content: content.trim()
      })
      .eq('id', noteId)
      .eq('user_id', user.id)
      .select(`
        id,
        content,
        content_type,
        summary_ai,
        created_at,
        updated_at,
        note_tags (
          tags (
            id,
            name
          )
        )
      `)
      .single()

    if (updateError) {
      console.error('Error updating note:', updateError)
      return NextResponse.json({ error: 'Failed to update note' }, { status: 500 })
    }

    const responseNote = {
      id: updatedNote.id,
      content: updatedNote.content,
      content_type: updatedNote.content_type,
      summary_ai: updatedNote.summary_ai,
      created_at: updatedNote.created_at,
      updated_at: updatedNote.updated_at,
      tags: updatedNote.note_tags?.map((nt: any) => nt.tags.name) || []
    }

    return NextResponse.json({ note: responseNote } as UpdateNoteResponse)
  } catch (error) {
    console.error('Error in PUT /api/notes/[id]:', error)
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 })
  }
}
