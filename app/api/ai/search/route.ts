import { NextRequest } from 'next/server'
import { searchNotesWithAI } from '@/lib/ai/search'
import { Note } from '@/types/notes'
import { withAuth, validateRequestBody, validateString, createErrorResponse, createSuccessResponse } from '@/lib/api/middleware'

interface SearchRequest {
  query: string
}

function validateSearchRequest(body: any): { isValid: boolean; error?: string; data?: SearchRequest } {
  const queryError = validateString(body.query, 'Search query')
  if (queryError) return { isValid: false, error: queryError }

  return {
    isValid: true,
    data: { query: body.query.trim() }
  }
}

export async function POST(request: NextRequest) {
  return withAuth(request, async (req, { user, supabase }) => {
    try {
      const body = await req.json()
      const validation = validateRequestBody(body, validateSearchRequest)

      if (!validation.isValid) {
        return createErrorResponse(validation.error!, 400)
      }

      const { query } = validation.data!

      const { data: notesData, error } = await supabase
        .from('notes')
        .select(`
          id,
          content,
          content_type,
          summary_ai,
          created_at,
          updated_at,
          note_tags (
            tags (
              id,
              name
            )
          )
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      if (error) {
        console.error('Error fetching notes for AI search:', error)
        return createErrorResponse('Failed to fetch notes')
      }

      const notes: Note[] = notesData.map((note: any) => ({
        id: note.id,
        content: note.content,
        content_type: note.content_type as 'text' | 'file' | 'link',
        summary_ai: note.summary_ai,
        created_at: note.created_at,
        updated_at: note.updated_at,
        tags: note.note_tags?.map((nt: any) => nt.tags.name) || []
      }))

      const foundNoteIds = await searchNotesWithAI(query, notes)

      const foundNotes = foundNoteIds
        .map(id => notes.find(note => note.id === id))
        .filter(Boolean) as Note[]

      return createSuccessResponse({
        notes: foundNotes,
        query: query
      })
    } catch (error) {
      console.error('Error in AI search:', error)
      return createErrorResponse('Internal server error')
    }
  })
}
