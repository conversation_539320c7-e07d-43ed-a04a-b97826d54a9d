import { NextRequest } from 'next/server'
import { analyzeNote } from '@/lib/ai/analyze'
import { withAuth, validateRequestBody, validateString, createErrorResponse, createSuccessResponse } from '@/lib/api/middleware'

interface AnalyzeNoteRequest {
  noteId: string
  content: string
}

function validateAnalyzeNoteRequest(body: any): { isValid: boolean; error?: string; data?: AnalyzeNoteRequest } {
  const noteIdError = validateString(body.noteId, 'Note ID')
  if (noteIdError) return { isValid: false, error: noteIdError }

  const contentError = validateString(body.content, 'Content')
  if (contentError) return { isValid: false, error: contentError }

  return {
    isValid: true,
    data: {
      noteId: body.noteId.trim(),
      content: body.content.trim()
    }
  }
}

export async function POST(request: NextRequest) {
  return withAuth(request, async (req, { user, supabase }) => {
    try {
      const body = await req.json()
      const validation = validateRequestBody(body, validateAnalyzeNoteRequest)

      if (!validation.isValid) {
        return createErrorResponse(validation.error!, 400)
      }

      const { noteId, content } = validation.data!

      const { data: note, error: noteError } = await supabase
        .from('notes')
        .select('id, user_id')
        .eq('id', noteId)
        .eq('user_id', user.id)
        .single()

      if (noteError || !note) {
        return createErrorResponse('Note not found', 404)
      }

      const analysis = await analyzeNote(content)

      const { data: result, error: updateError } = await supabase
        .rpc('update_note_with_tags', {
          p_note_id: noteId,
          p_user_id: user.id,
          p_summary: analysis.summary,
          p_tags: analysis.tags
        })

      if (updateError) {
        console.error('Error updating note with tags:', updateError)
        return createErrorResponse('Failed to update note')
      }

      if (!result || !result.success) {
        console.error('Function returned unsuccessful result:', result)
        return createErrorResponse('Failed to update note')
      }

      return createSuccessResponse({
        success: true,
        tags: result.tags || analysis.tags,
        summary: result.summary || analysis.summary
      })
    } catch (error) {
      console.error('Error in AI analyze note:', error)
      return createErrorResponse('Internal server error')
    }
  })
}
